#pragma once

#include "CoreMinimal.h"
#include "Toolkits/AssetEditorToolkit.h"
#include "IDetailCustomization.h"
#include "Widgets/SCompoundWidget.h"
#include "BiomeData.h"
#include "LandscapeStreamingProxy.h"
class UTexture2D;
class SImage;
class STextBlock;
class SButton;
class IDetailsView;

class FBiomeDataEditor : public FAssetEditorToolkit
{
public:
    static const FName BiomeDataEditorAppIdentifier;
    static const FName DetailsTabId;
    static const FName ViabilityPreviewTabId;

    FBiomeDataEditor();
    virtual ~FBiomeDataEditor();

    void InitBiomeDataEditor(const EToolkitMode::Type Mode, const TSharedPtr<IToolkitHost>& InitToolkitHost, UBiomeData* BiomeData);

    // FAssetEditorToolkit interface
    virtual FName GetToolkitFName() const override { return FName("BiomeDataEditor"); }
    virtual FText GetBaseToolkitName() const override { return FText::FromString("Biome Data Editor"); }
    virtual FString GetWorldCentricTabPrefix() const override { return "BiomeData"; }
    virtual FLinearColor GetWorldCentricTabColorScale() const override { return FLinearColor(0.0f, 0.5f, 0.0f); }
    virtual void RegisterTabSpawners(const TSharedRef<FTabManager>& TabManager) override;
    virtual void UnregisterTabSpawners(const TSharedRef<FTabManager>& TabManager) override;

    // Editor functions
    void BakeTerrainData();
    void PreviewSpeciesViability(int32 SpeciesIndex);
    void SetSelectedLandscapeProxy(ALandscapeStreamingProxy* InProxy) { SelectedLandscapeProxy = InProxy; }
    ALandscapeStreamingProxy* GetSelectedLandscapeProxy() const { return SelectedLandscapeProxy.Get(); }
    UBiomeData* GetBiomeData() const { return BiomeDataAsset; }

private:
    // Tab spawners
    TSharedRef<SDockTab> SpawnDetailsTab(const FSpawnTabArgs& Args);
    TSharedRef<SDockTab> SpawnViabilityPreviewTab(const FSpawnTabArgs& Args);

    // Helper functions
    void CreatePreviewTextures();
    float CalculateHeightAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const;
    float CalculateSlopeAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const;
    float CalculateAspectAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const;
    float CalculateCurvatureAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const;
    float CalculateOcclusionAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const;
    float CalculateFlowAccumulationAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape) const;
    float CalculateWindExposureAtLocation(const FVector& Location, ALandscapeStreamingProxy* Landscape, const FVector& WindDir) const;

    float CalculateViabilityFromAttributes(const FBiomeSpecies& Species, const TMap<FString, float>& Attributes, const FVector2D& WorldPos) const;
    FLinearColor GetViabilityColor(float Viability) const;

    // UI Components
    TSharedPtr<IDetailsView> DetailsView;
    TSharedPtr<class SBiomeViabilityPreviewWidget> ViabilityPreviewWidget;

    // The biome data being edited
    UBiomeData* BiomeDataAsset;

    // Selected landscape proxy
    TWeakObjectPtr<ALandscapeStreamingProxy> SelectedLandscapeProxy;

    // Preview textures
    UPROPERTY()
    UTexture2D* ViabilityPreviewTexture;

    UPROPERTY()
    TMap<FString, UTexture2D*> TerrainAttributeTextures;

    // Cached landscape bounds
    FVector CachedLandscapeOrigin;
    FVector CachedLandscapeExtent;
};

// Custom details customization for adding preview buttons
class FBiomeDataDetailsCustomization : public IDetailCustomization
{
public:
    static TSharedRef<IDetailCustomization> MakeInstance();

    virtual void CustomizeDetails(IDetailLayoutBuilder& DetailBuilder) override;
};

// Custom Slate widget for the preview
class SBiomeViabilityPreviewWidget : public SCompoundWidget
{
public:
    SLATE_BEGIN_ARGS(SBiomeViabilityPreviewWidget) {}
    SLATE_END_ARGS()

    void Construct(const FArguments& InArgs, FBiomeDataEditor* InEditor);
    void SetPreviewTexture(UTexture2D* InTexture);

private:
    FBiomeDataEditor* BiomeEditor;
    TSharedPtr<SImage> PreviewImage;
    TSharedPtr<STextBlock> InfoText;
    TSharedPtr<class SObjectPropertyEntryBox> LandscapeSelector;

    FReply OnBakeTerrainClicked();
    void OnLandscapeChanged(const FAssetData& AssetData);
};